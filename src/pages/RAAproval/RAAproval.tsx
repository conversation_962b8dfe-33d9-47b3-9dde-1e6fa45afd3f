import React, {useMemo, useRef, useState} from 'react';
import {useParams} from 'react-router-dom';
import PreviewFormDetails from '../CreateRA/PreviewFormDetails';
import {RiskForm} from '../../types';
import {useQuery} from '../../hooks/useQuery';
import {
  getRiskById,
  getTemplateById,
  setRiskRaLevel,
} from '../../services/services';
import {createRiskFormFromData} from '../../utils/helper';
import SearchDropdown from '../../components/SearchDropdown';
import {raLevels} from '../RAListing/components/RAFilters';
import {RaLevel} from '../../enums';
import {toast} from 'react-toastify';
import {AxiosError} from 'axios';

import '../../styles/components/ra-approval.scss';

export default function RAAproval() {
  const params = useParams<{id: string}>();
  const raId = String(params.id);

  const [form, setForm] = useState<RiskForm | null>(null);
  const [levelOfRA, setLevelOfRA] = useState<RaLevel | null>(null);
  const atRiskRef = useRef<any>(null);

  const {
    data: raData,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery(['risk', raId], () => getRiskById(raId), {
    enabled: !!raId,
    onSuccess: data => {
      if (data?.result) setForm(createRiskFormFromData(data.result));
    },
  });

  const templateId = raData?.result.template_id;
  const templateQ = useQuery(
    ['template', templateId],
    () => getTemplateById(String(templateId)),
    {enabled: Boolean(templateId)},
  );

  const $content = useMemo(() => {
    if (isLoading) {
      return <div>Loading...</div>;
    }

    if (isError) {
      return <div>Error: {error?.message || 'Failed to load data.'}</div>;
    }

    if (!form) {
      return <div>No data found.</div>;
    }

    return null;
  }, [isLoading, isError, error, form]);

  const setRaLevel = async () => {
    if (!levelOfRA || !raId) {
      toast.info('Please select a valid RA level before saving.');
      return;
    }

    try {
      const {message} = await setRiskRaLevel({
        ra_level: levelOfRA as RaLevel,
        risk_id: Number(raId),
      });
      setForm(prev => ({...prev, ra_level: levelOfRA} as RiskForm));
      toast.success(message);
    } catch (error) {
      toast.error(
        error instanceof AxiosError
          ? error.response?.data.message || error.message
          : 'Failed to set RA level',
      );
    }
  };

  const isCategoriesSameAsTemplate =
    (raData?.result?.risk_category?.length ?? 0) > 0
      ? templateQ.data?.result?.template_category?.every(templateCategory =>
          raData?.result?.risk_category.some(
            riskCategory =>
              riskCategory.category.id === templateCategory.category?.id,
          ),
        )
      : false;

  const isHazardsSameAsTemplate =
    (raData?.result?.risk_hazards?.length ?? 0) > 0
      ? templateQ.data?.result?.template_hazards?.every(templateHazard =>
          raData?.result?.risk_hazards.some(
            riskHazard =>
              riskHazard?.hazard_detail?.id ===
              templateHazard?.hazard_detail?.id,
          ),
        )
      : false;

  return (
    <div className="ra-approval-page">
      {$content ? (
        $content
      ) : (
        <PreviewFormDetails
          raId={Number(raId)}
          type="risk"
          form={form as unknown as RiskForm}
          setForm={setForm}
          atRiskRef={atRiskRef}
          handlePreviewPublush={() => {}}
          handleSaveToDraft={() => {}}
          isCategoriesSameAsTemplate={isCategoriesSameAsTemplate}
          isHazardsSameAsTemplate={isHazardsSameAsTemplate}
          riskApprover={raData?.result?.risk_approver}
          refechRA={refetch}
          breadcrumbOptions={{
            items: [
              {title: 'Risk Assessment', link: '/risk-assessment'},
              {title: form?.task_requiring_ra || ''}, // No link, just text
            ],
            options:
              !raData || form?.ra_level ? undefined : (
                <div className="d-flex align-items-center justify-content-end">
                  <SearchDropdown
                    placeholder="Set Level of R.A."
                    className="ra-approval-status-dropdown"
                    options={raLevels.filter(
                      item => item.value !== RaLevel.LEVEL_1_RA,
                    )}
                    selected={levelOfRA ? [levelOfRA] : null}
                    onChange={value => {
                      console.log('Selected value:', value);
                      if (value && value.length > 0) {
                        setLevelOfRA(Number(value[0]));
                      } else {
                        setLevelOfRA(null);
                      }
                    }}
                    multiple={false}
                  />
                  <button
                    disabled={!levelOfRA}
                    className="ra-approval-save-btn"
                    onClick={setRaLevel}
                  >
                    Save
                  </button>
                </div>
              ),
          }}
        />
      )}
    </div>
  );
}
