import _ from 'lodash';
import moment from 'moment';
import {RaLevel, RAStatus} from '../enums';

export const cleanObject = (
  value: Record<string, unknown>,
): Record<string, unknown> => {
  return _.omitBy(value, item => {
    if (Array.isArray(item)) {
      return item.length === 0;
    }
    if (typeof item === 'object' && item !== null) {
      return (
        Object.keys(cleanObject(item as Record<string, unknown>)).length === 0
      );
    }
    return _.isNil(item);
  });
};

export const parseDate = (
  date: string | Date | null | undefined = undefined,
  format = 'DD MMM YYYY',
): string | undefined => {
  return date && moment(date).isValid()
    ? moment(date).clone().format(format)
    : undefined;
};

export const getDateRangeFilters = (
  fieldKey: string,
  range?: [string | null, string | null] | null,
): Record<string, string> => {
  const filters: Record<string, string> = {};
  if (range?.[0]) filters[`${fieldKey}[start_date]`] = range[0];
  if (range?.[1]) filters[`${fieldKey}[end_date]`] = range[1];
  return filters;
};

export const vesselStatusAndLabelName: Record<string, string> = {
  active: 'Active Vessels',
  pending_handover: 'Pending Handover Vessels',
  handed_over: 'Handed Over Vessels',
};

export const assessorLabel: Record<number, string> = {
  1: 'Office',
  2: 'Vessel',
};

/**
 * Returns the value if it is not empty, otherwise returns the default placeholder '---'.
 * Empty values are: null, undefined, empty string, or string with only whitespace.
 * @param value The value to check.
 * @param placeholder The placeholder to use if value is empty. Defaults to '---'.
 */
export function withDefault<T = any>(
  value: T,
  placeholder: string = '---',
): T | string {
  if (
    value === null ||
    value === undefined ||
    (typeof value === 'string' && value.trim() === '')
  ) {
    return placeholder;
  }
  return value;
}

export const raLevelLabel: Record<number, string> = {
  [RaLevel.CRITICAL]: 'Critical',
  [RaLevel.LEVEL_1_RA]: 'Level 1 RA',
  [RaLevel.ROUTINE]: 'Routine',
  [RaLevel.SPECIAL]: 'Special',
};

export const raLevelColor: Record<number, 'red' | 'blue' | 'yellow' | 'green'> =
  {
    [RaLevel.CRITICAL]: 'red',
    [RaLevel.LEVEL_1_RA]: 'blue',
    [RaLevel.ROUTINE]: 'blue',
    [RaLevel.SPECIAL]: 'red',
  };

export const raStatusLabel: Record<number, string> = {
  [RAStatus.DRAFT]: 'Draft',
  [RAStatus.PUBLISHED]: 'Published',
  [RAStatus.APPROVED]: 'Approved',
  [RAStatus.REJECTED]: 'Rejected',
  [RAStatus.INACTIVE]: 'Inactive',
};
