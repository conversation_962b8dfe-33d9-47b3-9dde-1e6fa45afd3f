import React, {useMemo, useState} from 'react';
import {Card, Dropdown} from 'react-bootstrap';
import {toast} from 'react-toastify';
import {orderBy} from 'lodash';
import {ExclaimationIcon} from '../../utils/svgIcons';
import {RaLevel, RAStatus} from '../../enums';
import {AsyncSearchCrewMember} from '../../components/SearchCrewMember';
import ColoredTile, {ColoredTileTheme} from '../../components/ColoredTile';
import {raStatusLabel} from '../../utils/common';
import {OfficeApprover, RAItemFull} from '../../types';
import {assignApproversToRA, getOfficeApprovers} from '../../services/services';
import {getInitials} from '../../utils/user';
import {SvgUserOutline} from '../../components/icons';

import '../../styles/components/add-approver-card.scss';

interface AddApproverCardProps {
  riskId: number;
  raStatus: RAStatus;
  raLevel?: RaLevel;
  existingApprovers?: RAItemFull['risk_approver'];
  refetchRA: () => void;
}

type SelectedApprovers = Record<number, OfficeApprover | null>;

const AddApproverCard: React.FC<AddApproverCardProps> = ({
  riskId,
  raStatus,
  raLevel,
  existingApprovers,
  refetchRA,
}) => {
  const [selectedApprovers, setSelectedApprovers] = useState<SelectedApprovers>(
    {1: null, 2: null, 3: null},
  );

  const $content = useMemo(() => {
    if (!raLevel) {
      return (
        <div className="w-100 h-100 d-flex flex-column align-items-center justify-content-center gap-16px">
          <ExclaimationIcon />
          <div className="fs-14 text-muted text-center">
            Can only be added once you <br />
            submit the RA for Approval
          </div>
        </div>
      );
    }

    if ([RaLevel.CRITICAL, RaLevel.SPECIAL].includes(raLevel)) {
      return (
        <AssignApprovers
          approvers={selectedApprovers}
          onChange={setSelectedApprovers}
          existingApprovers={existingApprovers}
        />
      );
    }

    return null;
  }, [
    raLevel,
    JSON.stringify(selectedApprovers),
    setSelectedApprovers,
    existingApprovers,
  ]);

  const statusText = (
    [RAStatus.DRAFT, RAStatus.PUBLISHED].includes(raStatus)
      ? 'Pending'
      : raStatusLabel[raStatus]
  ) as string;

  let statusColor: ColoredTileTheme =
    raStatus === RAStatus.APPROVED ? 'green' : 'yellow';
  if (raStatus === RAStatus.REJECTED) {
    statusColor = 'red';
  }

  const assignApprovers = async (
    param: Parameters<typeof assignApproversToRA>[0],
  ) => {
    try {
      const {message} = await assignApproversToRA(param);
      toast.success(message || 'Approvers assigned successfully!');
      refetchRA();
    } catch (error) {
      toast.error('Failed to assign approvers. Please try again.');
    }
  };

  const selectedUserIds = Object.values(selectedApprovers)
    .filter(user => user)
    .map(user => user?.user_id)
    .filter(Boolean);
  const allAssigneseSelected = selectedUserIds.length === 3;

  return (
    <Card className="assign-approvers-card h-390p">
      <Card.Title className="d-flex align-items-center justify-content-between p-16px">
        <div className="fs-16 fw-600">Office Approval</div>
        <div className="d-flex align-items-center justify-content-between">
          {allAssigneseSelected && (
            <button
              className="assign-btn mr-2"
              disabled={!allAssigneseSelected}
              onClick={() =>
                assignApprovers({
                  risk_id: riskId,
                  approvers: Object.entries(selectedApprovers).map(
                    ([key, approver]) => ({
                      keycloak_id: approver?.user_id || '',
                      order: Number(key),
                      rank: approver?.rank,
                    }),
                  ),
                })
              }
            >
              Assign
            </button>
          )}
          <ColoredTile text={statusText} theme={statusColor} />
        </div>
      </Card.Title>
      <hr />
      <div className="card-body">{$content}</div>
    </Card>
  );
};

export default AddApproverCard;

const AssignApprovers: React.FC<{
  approvers: SelectedApprovers;
  onChange: React.Dispatch<React.SetStateAction<SelectedApprovers>>;
  existingApprovers?: RAItemFull['risk_approver'];
}> = ({approvers, onChange, existingApprovers}) => {
  const fetchOfficeApprovers = async (search?: string) => {
    if (!search || search.trim().length < 3) {
      return {options: [], originalData: []};
    }
    const data = await getOfficeApprovers(search);
    const selectedUserIds = Object.values(approvers)
      .map(user => user?.user_id)
      .filter(Boolean);

    return {
      options: data
        .filter(user => !selectedUserIds.includes(user.user_id))
        .map(user => ({
          id: user.user_id,
          full_name: [user.first_name, user.last_name]
            .filter(Boolean)
            .join(' '),
          subText: [user.email, user.rank].filter(Boolean).join(' • '),
        })),
      originalData: data,
    };
  };

  return (
    <div className="assign-approvers-container">
      {['First Reviewer', 'Second Reviewer', 'Third Reviewer'].map(
        (reviewer, index) => {
          const position = index + 1;
          const currentApprover = approvers[position];
          const assignedApprovers = existingApprovers?.filter(
            user => user.approval_order,
          );
          const defaultApprover = existingApprovers?.find(
            user => user.approval_order === null,
          );
          const existingApprover = orderBy(assignedApprovers || [], [
            'approval_order',
          ])?.[index];

          return (
            <div key={reviewer} className="assign-approver">
              <div className="d-flex align-items-center justify-content-between">
                <div className="approver-title mr-1">{reviewer}</div>
                <SvgUserOutline />
              </div>
              {existingApprover ? (
                <div className="user-list-item">
                  <div className="avatar">
                    {getInitials(existingApprover.user_name)}
                  </div>
                  <div className="d-flex align-items-center justify-content-between w-100">
                    <div className="user-info">
                      <div className="user-name">
                        {existingApprover.user_name}
                      </div>
                      <div className="user-details">
                        {[
                          existingApprover.job_title,
                          atob(existingApprover.user_email),
                        ]
                          .filter(Boolean)
                          .join(' • ')}
                      </div>
                    </div>
                    <div className="d-flex align-items-center justify-content-center">
                      <button className="assign-btn mr-2" onClick={() => {}}>
                        Reject
                      </button>
                      <Dropdown className="approval-btn">
                        <Dropdown.Toggle variant="primary">
                          Approval
                        </Dropdown.Toggle>
                        <Dropdown.Menu>
                          <Dropdown.Item onClick={() => {}}>
                            Approve
                          </Dropdown.Item>
                          <Dropdown.Item onClick={() => {}}>
                            Approve with Condition
                          </Dropdown.Item>
                        </Dropdown.Menu>
                      </Dropdown>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="assign-approver-input">
                  <AsyncSearchCrewMember<OfficeApprover>
                    value={
                      currentApprover?.email ? [currentApprover.email] : []
                    }
                    placeholder="Search Name, Rank or Email ID"
                    onChange={(selectedUserIds, originalData) => {
                      const selectedUserId = selectedUserIds[0];

                      if (selectedUserId && originalData?.length) {
                        const selectedUser = originalData.find(
                          user => user.user_id === selectedUserId,
                        );
                        if (selectedUser) {
                          onChange(prev => ({
                            ...prev,
                            [position]: selectedUser,
                          }));
                        }
                      } else {
                        onChange(prev => ({...prev, [position]: null}));
                      }
                    }}
                    fetchQuery={fetchOfficeApprovers}
                    uniqueQueryKey={`getOfficeApprovers-${index}`}
                  />
                </div>
              )}
            </div>
          );
        },
      )}
    </div>
  );
};
