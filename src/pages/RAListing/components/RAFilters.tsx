import React, {useEffect, useMemo, useState} from 'react';
import {toast} from 'react-toastify';
import {Row, Col, Form} from 'react-bootstrap';
import SearchInput from '../../../components/SearchInput';
import {
  getOfficesList,
  getRAStringOptions,
  getVesselsList,
} from '../../../services/services';
import CustomDatePickerWithRange from '../../../components/CustomDatePickerWithRange';
import {RaLevel, RAStatus} from '../../../enums';
import RAMoreFiltersDrawer from './RAMoreFiltersDrawer';
import VesselAndOfficeDropdown from '../../../components/VesselAndOfficeDropdown';
import SearchDropdown from '../../../components/SearchDropdown';
import {groupBy} from 'lodash';
import {raLevelLabel, vesselStatusAndLabelName} from '../../../utils/common';

import '../../../styles/components/ra-filters.scss';

const statuses = [
  {value: RAStatus.APPROVED, label: 'Approved'},
  // {value: RAStatus.DRAFT, label: 'Draft'}, - Draft status is not used in the filter
  {value: RAStatus.INACTIVE, label: 'Inactive'},
  {value: RAStatus.PUBLISHED, label: 'Published'},
  {value: RAStatus.REJECTED, label: 'Rejected'},
];

export const raLevels = Object.entries(raLevelLabel).map(([key, value]) => ({
  label: value,
  value: Number(key),
}));

export type FilterOption = {
  label: string;
  value: string | number;
  full_name?: string;
  designation?: string;
  status?: string;
};

export type DateRangeValue = [string | null, string | null] | null;

export type FilterValue =
  | string
  | null
  | FilterOption
  | number[]
  | (string | number)[]
  | DateRangeValue
  | {
      vessel_id: number[] | null;
      office_id: number[] | null;
    };

export interface RAFilterValues {
  search: string | null;
  approval_status: (string | number)[];
  vessel_or_office: {
    vessel_id: number[] | null;
    office_id: number[] | null;
  } | null;
  vessel_category: number[];
  ra_level: (string | number)[];
  submitted_on: DateRangeValue;
  assessment_date: DateRangeValue;
  approval_date: DateRangeValue;
}

export const raFiltersInitialState: RAFilterValues = {
  search: null,
  approval_status: [],
  vessel_or_office: null,
  vessel_category: [],
  ra_level: [],
  submitted_on: null,
  approval_date: null,
  assessment_date: null,
};

export interface RAFiltersProps {
  filters: RAFilterValues;
  onFilterChange: (key: keyof RAFilterValues, value: FilterValue) => void;
}

export const RAFilters: React.FC<RAFiltersProps> = ({
  filters,
  onFilterChange,
}) => {
  const [vessels, setVessels] = useState<FilterOption[]>([]);
  const [offices, setOffices] = useState<FilterOption[]>([]);
  const [vesselCategories, setVesselCategories] = useState<FilterOption[]>([]);

  useEffect(() => {
    const loadBasicDetails = async () => {
      try {
        const [raVesselCategory, raVessel, raOffice] = await Promise.all([
          getRAStringOptions('vessel_category'),
          getVesselsList(),
          getOfficesList(),
        ]);
        setVessels(
          raVessel.map(item => ({
            label: item.name,
            value: item.vessel.id,
            status: item.status,
          })),
        );
        setOffices(
          raOffice.map(item => ({
            label: item.value,
            value: item.id,
          })),
        );
        setVesselCategories(
          raVesselCategory.result.map(item => ({
            label: item,
            value: item,
          })),
        );
      } catch (error) {
        console.error('Error loading data:', error);
        toast.error('Failed to load data. Please try again later.');
      }
    };
    loadBasicDetails();
  }, []);

  const filterConfig = useMemo(
    () =>
      getRaBasicFiltersFormConfig(
        {filters, onFilterChange},
        {vessels, vesselCategories, offices},
      ),
    [filters, onFilterChange, vessels, vesselCategories, offices],
  );

  return (
    <div className="ra-filters">
      <Row className="equal-width-cols">
        {filterConfig.map(({key, component}) => (
          <Col key={key} className="col-equal">
            <Form.Group controlId={key}>{component}</Form.Group>
          </Col>
        ))}
        <Col className="col-equal">
          <Form.Group>
            <RAMoreFiltersDrawer
              onFilterChange={onFilterChange}
              optionsData={{
                vessels,
                vesselCategories,
                offices,
              }}
            />
          </Form.Group>
        </Col>
      </Row>
    </div>
  );
};

// Helper to get vessel/office options
function getVesselOfficeOptions(
  vessels: FilterOption[],
  offices: FilterOption[],
) {
  return [
    ...Object.entries(groupBy(vessels, 'status'))
      .sort((a, b) => a[0].localeCompare(b[0]))
      .map(([status, vessels]) => ({
        label: vesselStatusAndLabelName[String(status)] || status,
        data: vessels.map(vessel => ({
          id: vessel.value,
          name: vessel.label,
        })),
      })),
    {
      label: 'Offices',
      data: offices.map((office: FilterOption) => ({
        id: office.value,
        name: office.label,
      })),
    },
  ];
}

function getSearchFilterConfig(
  filters: RAFilterValues,
  onFilterChange: RAFiltersProps['onFilterChange'],
) {
  return {
    key: 'search' as const,
    label: 'Search by Task Name',
    component: (
      <SearchInput
        value={filters.search || ''}
        onSearch={value => onFilterChange('search', value || null)}
        placeholder="Search by Task Name"
      />
    ),
  };
}

function getDropdownFilterConfig({
  key,
  label,
  options,
  value,
  onChange,
}: {
  key: string;
  label: string;
  options: FilterOption[];
  value: any;
  onChange: (value: any) => void;
}) {
  return {
    key,
    label,
    component: (
      <SearchDropdown
        placeholder={label}
        options={options}
        selected={value}
        onChange={value => onChange(value)}
        multiple={false}
      />
    ),
  };
}

function getDateRangeFilterConfig({
  key,
  label,
  controlId,
  value,
  onChange,
  placeholder,
}: {
  key: string;
  label: string;
  controlId: string;
  value: DateRangeValue;
  onChange: (dates: [string | null, string | null]) => void;
  placeholder: string;
}) {
  // CustomDatePickerWithRange emits [string | undefined, string | undefined]
  // We need to convert undefined to null for our state
  const handleChange = (dates: [string | undefined, string | undefined]) => {
    const [start, end] = dates;
    onChange([start ?? null, end ?? null]);
  };
  return {
    key,
    label,
    component: (
      <CustomDatePickerWithRange
        controlId={controlId}
        placeholder={placeholder}
        startDate={value?.[0] ? new Date(value[0]!) : undefined}
        endDate={value?.[1] ? new Date(value[1]!) : undefined}
        onChange={handleChange}
      />
    ),
  };
}

export const getRaBasicFiltersFormConfig = (
  raFilterProps: RAFiltersProps,
  optionsData: {
    vessels: FilterOption[];
    vesselCategories: FilterOption[];
    offices: FilterOption[];
  },
) => {
  const {filters, onFilterChange} = raFilterProps;
  const {vessels, vesselCategories, offices} = optionsData;
  return [
    getSearchFilterConfig(filters, onFilterChange),
    getDropdownFilterConfig({
      key: 'approval_status',
      label: 'Approval Status',
      options: statuses,
      value: filters.approval_status,
      onChange: value => onFilterChange('approval_status', value),
    }),
    {
      key: 'vessel_id' as const,
      label: 'Vessel/Office Name',
      component: (
        <VesselAndOfficeDropdown
          value={filters.vessel_or_office}
          options={getVesselOfficeOptions(vessels, offices)}
          placeholder="Select Vessel/Office"
          onChange={value => onFilterChange('vessel_or_office', value)}
        />
      ),
    },
    getDropdownFilterConfig({
      key: 'vessel_category',
      label: 'Vessel Category',
      options: vesselCategories,
      value: filters.vessel_category,
      onChange: value => onFilterChange('vessel_category', value),
    }),
    getDropdownFilterConfig({
      key: 'ra_level',
      label: 'Level of RA',
      options: raLevels,
      value: filters.ra_level,
      onChange: value => onFilterChange('ra_level', value),
    }),
    getDateRangeFilterConfig({
      key: 'submission_date',
      label: 'Submitted on',
      controlId: 'ra_filters_submitted_on',
      value: filters.submitted_on,
      onChange: ([start, end]) =>
        onFilterChange('submitted_on', [start ?? null, end ?? null]),
      placeholder: 'Submitted on',
    }),
  ];
};
